#!/usr/bin/env python3
"""
Test script to verify the monitoring fix for AutoGapfiller.py
This script simulates the monitoring behavior and tests the exit conditions.
"""

import os
import sys
import time
import subprocess
import tempfile
from unittest.mock import patch, MagicMock

def test_lsf_job_status_check():
    """Test the LSF job status checking function"""
    print("Testing LSF job status check...")
    
    # Import the function from AutoGapfiller
    sys.path.insert(0, '.')
    from AutoGapfiller import check_lsf_jobs_status
    
    # Test with a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        active_count, active_jobs = check_lsf_jobs_status(temp_dir)
        print(f"Active jobs found: {active_count}")
        if active_jobs:
            for job_id, job_name, status in active_jobs[:3]:
                print(f"  Job: {job_id} ({job_name}) - {status}")
    
    return True

def test_gap_completion_check():
    """Test the gap completion checking function"""
    print("Testing gap completion check...")
    
    sys.path.insert(0, '.')
    from AutoGapfiller import check_all_gaps_finished
    
    # Create a mock directory structure
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create gap.log file
        gap_log = os.path.join(temp_dir, 'gap.log')
        with open(gap_log, 'w') as f:
            f.write("Chromosome\tId\tStart\tEnd\tGapLen\tLeftLen\tRightLen\n")
            f.write("chr1A_TA299\t1\t1000\t2000\t1000\t500\t500\n")
            f.write("chr1A_TA299\t2\t3000\t4000\t1000\t500\t500\n")
        
        # Create DEGAP2.0_Output directory
        degap_output = os.path.join(temp_dir, 'DEGAP2.0_Output')
        os.makedirs(degap_output)
        
        # Create some result directories
        gap1_left = os.path.join(degap_output, 'chr1A_TA299.1.left')
        gap1_right = os.path.join(degap_output, 'chr1A_TA299.1.right')
        os.makedirs(gap1_left)
        os.makedirs(gap1_right)
        
        # Create final.fa files
        with open(os.path.join(gap1_left, 'chr1A_TA299.1.final.fa'), 'w') as f:
            f.write(">test\nATCG\n")
        with open(os.path.join(gap1_right, 'chr1A_TA299.1.final.fa'), 'w') as f:
            f.write(">test\nATCG\n")
        
        # Test completion check
        finished, total, completed = check_all_gaps_finished(temp_dir, 'all')
        print(f"Completion check result: {completed}/{total} finished={finished}")
    
    return True

def test_monitoring_timeout():
    """Test the monitoring timeout mechanism"""
    print("Testing monitoring timeout mechanism...")
    
    # This would test the timeout logic in the main monitoring loop
    # For now, just verify the logic exists
    
    max_hours = 48
    start_time = time.time()
    
    # Simulate some elapsed time
    elapsed_hours = (time.time() - start_time) / 3600
    
    if elapsed_hours <= max_hours:
        print(f"Timeout check working: {elapsed_hours:.3f}h < {max_hours}h")
        return True
    else:
        print(f"Timeout would trigger: {elapsed_hours:.3f}h >= {max_hours}h")
        return False

def main():
    """Run all tests"""
    print("=== Testing AutoGapfiller monitoring fixes ===\n")
    
    tests = [
        ("LSF Job Status Check", test_lsf_job_status_check),
        ("Gap Completion Check", test_gap_completion_check),
        ("Monitoring Timeout", test_monitoring_timeout),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✓ {test_name}: {'PASSED' if result else 'FAILED'}\n")
        except Exception as e:
            print(f"✗ {test_name}: ERROR - {e}\n")
            results.append((test_name, False))
    
    print("=== Test Results ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        print(f"{status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n✓ All monitoring fixes appear to be working correctly!")
        return 0
    else:
        print(f"\n✗ {total - passed} test(s) failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
